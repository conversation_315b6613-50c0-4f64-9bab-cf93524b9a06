<route lang="jsonc" type="page">
{
  "layout": "default",
  "style": {
    "navigationStyle": "custom",
    "navigationBarTitleText": "登录"
  }
}
</route>

<script lang="ts" setup>
import { ref } from 'vue'

const isShaking = ref(false)
const value = ref<boolean>(false)
// 一键登录
function handleQuickLogin() {
  if (!value.value) {
    isShaking.value = true
    setTimeout(() => {
      isShaking.value = false
    }, 600)

    // 显示提示
    setTimeout(() => {
      uni.showToast({
        title: '请先阅读并同意服务协议',
        icon: 'none',
        duration: 2000,
      })
    }, 100)

    return
  }

  uni.showLoading({ title: '登录中...' })
  setTimeout(() => {
    uni.hideLoading()
    uni.showToast({ title: '登录成功', icon: 'success' })
    setTimeout(() => {
      uni.switchTab({ url: '/pages/index/index' })
    }, 1500)
  }, 2000)
}

function handleChange({ value }) {
  console.log(value)
}
// 用户协议
function handleUserAgreement() {
  uni.showToast({ title: '用户协议', icon: 'none' })
}

// 隐私政策
function handlePrivacyPolicy() {
  uni.showToast({ title: '隐私政策', icon: 'none' })
}
</script>

<template>
  <view class="relative min-h-screen overflow-hidden from-blue-50 to-white bg-gradient-to-b">
    <view class="absolute inset-0">
      <view class="absolute left-0 top-0 h-80 w-full opacity-30">
        <view class="absolute left-10 top-20 h-40 w-40 rounded-full bg-green-200 blur-xl" />
        <view class="absolute right-20 top-10 h-60 w-60 rounded-full bg-green-100 blur-2xl" />
        <view class="absolute left-1/3 top-40 h-50 w-50 rounded-full bg-blue-100 blur-xl" />
      </view>

      <view class="absolute left-20 top-32 h-10 w-20 rounded-full bg-white opacity-60" />
      <view class="absolute right-16 top-28 h-8 w-16 rounded-full bg-white opacity-50" />
    </view>

    <view class="relative z-10 flex flex-col items-center px-8 pt-32">
      <view class="mb-16">
        <view class="mb-6 flex justify-center">
          <view class="relative">
            <view class="absolute inset-0 animate-ping rounded-full bg-blue-100 opacity-20" />
            <view
              class="relative h-20 w-20 flex items-center justify-center rounded-full from-green-400 to-blue-400 bg-gradient-to-br"
            >
              <view class="relative h-12 w-12">
                <view class="absolute left-2 top-2 h-8 w-8 rotate-12 transform rounded-full bg-white opacity-80" />
                <view class="absolute right-2 top-1 h-8 w-6 transform rounded-full bg-white opacity-60 -rotate-12" />
              </view>
            </view>
          </view>
        </view>

        <view class="text-center">
          <text class="text-4xl text-gray-800 font-bold tracking-wide">不老客</text>
        </view>
      </view>

      <view class="mb-32 max-w-sm w-full">
        <view
          class="w-full flex items-center justify-center rounded-full shadow-lg transition-transform active:scale-95"
          style="height: 100rpx; background: linear-gradient(135deg, #4ade80 0%, #06b6d4 100%);"
          @click="handleQuickLogin"
        >
          <text class="text-lg text-white font-semibold">本机号码一键登录</text>
        </view>
      </view>

      <view class="absolute bottom-12 left-8 right-8">
        <view class="mb-4 flex items-center justify-center" :class="isShaking ? 'animate-shake' : ''">
          <wd-checkbox v-model="value" checked-color="#4ade80" @change="handleChange" />
          <view class="flex-1">
            <text class="text-xs text-gray-400">登录即表明同意</text>
            <text class="mx-1 text-xs text-#4ade80" @click="handleUserAgreement">《用户协议》</text>
            <text class="text-xs text-gray-400">和</text>
            <text class="mx-1 text-xs text-#4ade80" @click="handlePrivacyPolicy">《隐私政策》</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
@keyframes shake {
  0%,
  100% {
    transform: translateX(0);
  }

  10%,
  30%,
  50%,
  70%,
  90% {
    transform: translateX(-5px);
  }

  20%,
  40%,
  60%,
  80% {
    transform: translateX(5px);
  }
}

.animate-shake {
  animation: shake 0.6s ease-in-out;
}

// 自定义动画
@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }

  50% {
    transform: translateY(-10px);
  }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

// 渐变背景增强
.bg-mountain {
  background: linear-gradient(
    135deg,
    rgba(134, 239, 172, 0.3) 0%,
    rgba(59, 130, 246, 0.2) 50%,
    rgba(255, 255, 255, 0.1) 100%
  );
}

// 按钮悬停效果
.login-btn {
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 4px 12px rgba(34, 197, 94, 0.3);
  }
}

.other-login-btn {
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
    background-color: rgba(34, 197, 94, 0.1);
  }
}
</style>
