<route lang="jsonc" type="page">
{
  "style": {
    "navigationStyle": "custom",
    "navigationBarTitleText": "我的"
  }
}
</route>

<script lang="ts" setup>
import { ref } from 'vue'

// 用户信息 - 使用与首页相同的结构
const userInfo = ref({
  avatar: '/static/images/default-avatar.png',
  nickname: 'Uninauto',
  idNumber: '微信身份认证',
})

// 我参加的活动列表
const myActivities = ref([
  {
    id: 1,
    title: '三点班组',
    status: '进行中',
    time: '今天 14:30',
    participants: 12,
  },
  {
    id: 2,
    title: 'Tab Boror',
    status: '已结束',
    time: '昨天 16:00',
    participants: 8,
  },
  {
    id: 3,
    title: '远程三不许话',
    status: '即将开始',
    time: '明天 10:00',
    participants: 15,
  },
])

const settingsList = ref([
  {
    title: '意见反馈',
    desc: '帮助我们改进产品',
    showBadge: false,
    badgeCount: 0,
  },
  {
    title: '隐私政策',
    desc: '了解我们如何保护您的隐私',
    showBadge: false,
    badgeCount: 0,
  },
])

// 点击用户卡片
function handleUserCardClick() {
  uni.navigateTo({ url: '/pages/profile/profile' })
}

// 点击设置项
function handleSettingClick(item: any) {
  if (item.title === '意见反馈') {
    uni.navigateTo({ url: '/pages/feedback/feedback' })
  }
  else if (item.title === '隐私政策') {
    uni.navigateTo({ url: '/pages/privacy/privacy' })
  }
}

// 点击活动项
function handleActivityClick(activity: any) {
  uni.showToast({
    title: `查看活动: ${activity.title}`,
    icon: 'none',
  })
}

// 获取活动状态样式
function getStatusClass(status: string) {
  switch (status) {
    case '进行中':
      return 'status-active'
    case '已结束':
      return 'status-finished'
    case '即将开始':
      return 'status-upcoming'
    default:
      return ''
  }
}

// 退出登录
function handleLogout() {
  uni.showModal({
    title: '提示',
    content: '确定要退出登录吗？',
    success: (res) => {
      if (res.confirm) {
        uni.showToast({ title: '已退出登录', icon: 'success' })
        setTimeout(() => {
          uni.reLaunch({ url: '/pages/login/login' })
        }, 1500)
      }
    },
  })
}
</script>

<template>
  <view class="me-page">
    <!-- 头部区域 - 参考首页风格 -->
    <view class="header-section">
      <view class="status-bar" />
      <view class="header-title">
        我的
      </view>
    </view>

    <view class="content-section">
      <!-- 用户信息卡片 - 与首页完全一致 -->
      <view class="user-card" @click="handleUserCardClick">
        <view class="user-info">
          <image class="avatar" :src="userInfo.avatar" mode="aspectFill" />
          <view class="user-details">
            <view class="nickname">
              {{ userInfo.nickname }}
            </view>
            <view class="id-number">
              {{ userInfo.idNumber }}
            </view>
          </view>
        </view>
        <view class="join-btn">
          <text>联动者</text>
        </view>
      </view>

      <!-- 我参加的活动 -->
      <view class="activity-section">
        <view class="section-header">
          <view class="section-title">
            我参加的活动
          </view>
          <view class="more-btn" @click="handleSettingClick({ title: '我参加的活动' })">
            <text>查看全部</text>
          </view>
        </view>

        <view class="activity-list">
          <view
            v-for="activity in myActivities.slice(0, 3)"
            :key="activity.id"
            class="activity-item"
            @click="handleActivityClick(activity)"
          >
            <view class="activity-info">
              <view class="activity-title">
                {{ activity.title }}
              </view>
              <view class="activity-meta">
                <text class="activity-time">{{ activity.time }}</text>
                <text class="activity-participants">{{ activity.participants }}人参与</text>
              </view>
            </view>
            <view class="activity-status" :class="getStatusClass(activity.status)">
              {{ activity.status }}
            </view>
          </view>
        </view>
      </view>

      <!-- 设置菜单 - 参考微信风格 -->
      <view class="settings-section">
        <view class="settings-group">
          <view
            v-for="(item, index) in settingsList"
            :key="index"
            class="setting-item"
            @click="handleSettingClick(item)"
          >
            <view class="setting-left">
              <view class="setting-icon">
                <uni-icons :type="item.icon" size="20" color="#6B7280" />
              </view>
              <view class="setting-content">
                <view class="setting-title">
                  {{ item.title }}
                </view>
                <view v-if="item.desc" class="setting-desc">
                  {{ item.desc }}
                </view>
              </view>
            </view>
            <view class="setting-right">
              <view v-if="item.showBadge && item.badgeCount > 0" class="badge">
                {{ item.badgeCount }}
              </view>
              <uni-icons type="right" size="16" color="#C7C7CC" />
            </view>
          </view>
        </view>
      </view>

      <!-- 退出登录按钮 -->
      <view class="logout-section">
        <view class="logout-btn" @click="handleLogout">
          <text class="logout-text">退出登录</text>
        </view>
      </view>

      <!-- 底部安全距离 -->
      <view class="safe-area-bottom" />
    </view>
  </view>
</template>

<style lang="scss" scoped>
// 页面背景 - 参考首页风格
.me-page {
  background: linear-gradient(to bottom, #ffffff, #f7f8fa 300rpx);
  min-height: 100vh;
}

// 头部区域
.header-section {
  position: relative;
  background: linear-gradient(135deg, #ffb347 0%, #ff6b6b 100%);
  padding: 100rpx 40rpx 140rpx;
  border-radius: 0 0 80rpx 80rpx;
  overflow: hidden;

  // 装饰性背景圆圈
  &::before,
  &::after {
    content: '';
    position: absolute;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.1);
    z-index: 1;
  }
  &::before {
    width: 300rpx;
    height: 300rpx;
    top: -100rpx;
    right: -100rpx;
  }
  &::after {
    width: 150rpx;
    height: 150rpx;
    bottom: 20rpx;
    left: -50rpx;
  }

  .status-bar {
    height: 88rpx;
  }

  .header-title {
    position: relative;
    z-index: 2;
    font-size: 56rpx;
    font-weight: bold;
    color: #fff;
    text-align: center;
    text-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  }
}

.content-section {
  padding: 0 30rpx;
}

// 用户信息卡片 - 与首页完全一致
.user-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: -100rpx;
  position: relative;
  z-index: 10;
  padding: 40rpx;
  border-radius: 32rpx;
  background-color: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.5);
  box-shadow: 0 16rpx 40rpx rgba(100, 100, 100, 0.12);

  .user-info {
    display: flex;
    align-items: center;
    gap: 24rpx;

    .avatar {
      width: 110rpx;
      height: 110rpx;
      border-radius: 50%;
      border: 4rpx solid #fff;
    }
    .user-details {
      .nickname {
        font-size: 36rpx;
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 8rpx;
      }
      .id-number {
        font-size: 24rpx;
        color: #4a5568;
      }
    }
  }

  .join-btn {
    padding: 18rpx 36rpx;
    background: linear-gradient(135deg, #ffb347 0%, #ff6b6b 100%);
    color: #fff;
    border-radius: 30rpx;
    font-size: 26rpx;
    font-weight: 500;
    box-shadow: 0 8rpx 16rpx rgba(255, 125, 106, 0.35);
  }
}

// 活动区域
.activity-section {
  margin-top: 50rpx;
  padding-bottom: 40rpx;

  .section-header {
    margin-bottom: 30rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .section-title {
      font-size: 38rpx;
      font-weight: 600;
      color: #1f2937;
    }
    .more-btn {
      font-size: 26rpx;
      color: #9ca3af;
    }
  }

  .activity-list {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 24rpx;
    overflow: hidden;
    backdrop-filter: blur(10rpx);

    .activity-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 32rpx;
      border-bottom: 1rpx solid #f3f4f6;

      &:last-child {
        border-bottom: none;
      }

      .activity-info {
        flex: 1;

        .activity-title {
          font-size: 32rpx;
          font-weight: 500;
          color: #1f2937;
          margin-bottom: 8rpx;
        }

        .activity-meta {
          display: flex;
          gap: 20rpx;
          font-size: 24rpx;
          color: #9ca3af;
        }
      }

      .activity-status {
        padding: 8rpx 16rpx;
        border-radius: 16rpx;
        font-size: 22rpx;
        font-weight: 500;

        &.status-active {
          background: #dcfce7;
          color: #16a34a;
        }

        &.status-finished {
          background: #f3f4f6;
          color: #6b7280;
        }

        &.status-upcoming {
          background: #fef3c7;
          color: #d97706;
        }
      }
    }
  }
}

// 设置菜单 - 参考微信风格
.settings-section {
  margin-top: 40rpx;
  padding-bottom: 40rpx;

  .settings-group {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 24rpx;
    overflow: hidden;
    backdrop-filter: blur(10rpx);

    .setting-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 32rpx;
      border-bottom: 1rpx solid #f3f4f6;
      transition: background-color 0.2s ease;

      &:last-child {
        border-bottom: none;
      }

      &:active {
        background-color: rgba(0, 0, 0, 0.05);
      }

      .setting-left {
        display: flex;
        align-items: center;
        flex: 1;

        .setting-icon {
          width: 56rpx;
          height: 56rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          background: #f8f9fa;
          border-radius: 16rpx;
          margin-right: 24rpx;
        }

        .setting-content {
          flex: 1;

          .setting-title {
            font-size: 32rpx;
            font-weight: 500;
            color: #1f2937;
            margin-bottom: 4rpx;
          }

          .setting-desc {
            font-size: 24rpx;
            color: #9ca3af;
          }
        }
      }

      .setting-right {
        display: flex;
        align-items: center;
        gap: 16rpx;

        .badge {
          background: #ff4757;
          color: #fff;
          font-size: 20rpx;
          padding: 4rpx 12rpx;
          border-radius: 20rpx;
          min-width: 32rpx;
          text-align: center;
        }
      }
    }
  }
}

// 退出登录按钮
.logout-section {
  margin-top: 40rpx;
  padding: 0 30rpx;

  .logout-btn {
    width: 100%;
    height: 96rpx;
    background: #fff;
    border-radius: 24rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;

    &:active {
      transform: scale(0.98);
      background: #f8f9fa;
    }

    .logout-text {
      font-size: 32rpx;
      font-weight: 500;
      color: #ff4757;
    }
  }
}

// 底部安全距离
.safe-area-bottom {
  height: 60rpx;
}
</style>
