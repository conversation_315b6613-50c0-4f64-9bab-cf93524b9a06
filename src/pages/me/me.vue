<route lang="jsonc" type="page">
{
  "style": {
    "navigationStyle": "custom",
    "navigationBarTitleText": "我的"
  }
}
</route>

<script lang="ts" setup>
import { ref } from 'vue'

// 用户信息
const userInfo = ref({
  avatar: '/static/images/avatar.png',
  nickname: '黑马青年',
  title: '产品设计师 · IP 黑地 浙江',
  points: 20,
})

// 统计信息
const stats = ref({
  timeSpent: '我的时间戳',
  timeDesc: '我的时间有效可循',
  storage: '我的空间',
  storageDesc: '剩余 704G 存储量',
})

// 任务进度
const taskProgress = ref({
  title: '时间契缘',
  current: 3,
  total: 8,
  taskName: '收获 3 个答案',
  taskDesc: '快去时间契缘空间查看吧！',
})

// 设置菜单
const settingsList = ref([
  { title: '我参加的活动', icon: 'calendar' },
  { title: '意见反馈', icon: 'chat' },
  { title: '隐私政策', icon: 'locked' },
])

// 点击菜单项
function handleMenuClick(item: any) {
  uni.showToast({
    title: `点击了${item.title}`,
    icon: 'none',
  })
}

// 点击设置项
function handleSettingClick(item: any) {
  if (item.title === '我参加的活动') {
    uni.navigateTo({ url: '/pages/activity/my-activity' })
  }
  else if (item.title === '意见反馈') {
    uni.navigateTo({ url: '/pages/feedback/feedback' })
  }
  else if (item.title === '隐私政策') {
    uni.navigateTo({ url: '/pages/privacy/privacy' })
  }
}

// 退出登录
function handleLogout() {
  uni.showModal({
    title: '提示',
    content: '确定要退出登录吗？',
    success: (res) => {
      if (res.confirm) {
        uni.showToast({ title: '已退出登录', icon: 'success' })
        setTimeout(() => {
          uni.reLaunch({ url: '/pages/login/login' })
        }, 1500)
      }
    },
  })
}

// 点击任务卡片
function handleTaskClick() {
  uni.showToast({
    title: '跳转到时间契缘空间',
    icon: 'none',
  })
}

// 点击头像
function handleAvatarClick() {
  uni.navigateTo({ url: '/pages/profile/profile' })
}
</script>

<template>
  <view class="min-h-screen from-purple-400 via-blue-300 to-green-200 bg-gradient-to-br">
    <!-- 状态栏占位 -->
    <view class="h-11" />
    <!-- 用户信息卡片 -->
    <view class="mx-6 mb-6">
      <view class="flex items-center justify-between rounded-2xl bg-white/90 p-6 backdrop-blur-sm">
        <view class="flex items-center">
          <!-- 头像 -->
          <view class="mr-4" @click="handleAvatarClick">
            <view class="relative h-16 w-16 overflow-hidden rounded-full from-blue-400 to-cyan-400 bg-gradient-to-br">
              <view class="absolute inset-2 flex items-center justify-center rounded-full bg-white">
                <text class="text-2xl">😊</text>
              </view>
            </view>
          </view>

          <!-- 用户信息 -->
          <view>
            <text class="mb-1 block text-lg text-gray-800 font-bold">{{ userInfo.nickname }}</text>
            <text class="text-sm text-gray-500">{{ userInfo.title }}</text>
          </view>
        </view>

        <!-- 积分 -->
        <view class="flex items-center rounded-full bg-orange-100 px-3 py-1">
          <view class="mr-1 h-4 w-4 rounded-full bg-orange-400" />
          <text class="text-sm text-orange-600 font-semibold">+{{ userInfo.points }}</text>
        </view>
      </view>
    </view>

    <!-- 统计卡片 -->
    <view class="mx-6 mb-6">
      <view class="grid grid-cols-2 gap-4">
        <!-- 时间戳 -->
        <view class="rounded-xl bg-white/90 p-4 backdrop-blur-sm">
          <view class="mb-2 flex items-center">
            <view class="mr-2 h-6 w-6 flex items-center justify-center rounded-full bg-blue-500">
              <uni-icons type="loop" size="14" color="#fff" />
            </view>
            <text class="text-gray-800 font-semibold">{{ stats.timeSpent }}</text>
          </view>
          <text class="text-xs text-gray-500">{{ stats.timeDesc }}</text>
        </view>

        <!-- 存储空间 -->
        <view class="rounded-xl bg-white/90 p-4 backdrop-blur-sm">
          <view class="mb-2 flex items-center">
            <view class="mr-2 h-6 w-6 flex items-center justify-center rounded-full bg-orange-500">
              <uni-icons type="folder-add" size="14" color="#fff" />
            </view>
            <text class="text-gray-800 font-semibold">{{ stats.storage }}</text>
          </view>
          <text class="text-xs text-gray-500">{{ stats.storageDesc }}</text>
        </view>
      </view>
    </view>

    <!-- 任务进度卡片 -->
    <view class="mx-6 mb-6">
      <view class="rounded-2xl from-blue-500 to-blue-600 bg-gradient-to-r p-6 text-white">
        <view class="mb-4 flex items-center justify-between">
          <text class="text-lg font-bold">{{ taskProgress.title }}</text>
          <view class="flex items-center">
            <view class="mr-2 h-2 w-20 rounded-full bg-white/30">
              <view
                class="h-full rounded-full bg-green-400 transition-all duration-300"
                :style="`width: ${(taskProgress.current / taskProgress.total) * 100}%`"
              />
            </view>
            <text class="text-sm">{{ taskProgress.current }}/{{ taskProgress.total }}</text>
          </view>
        </view>

        <view
          class="rounded-xl bg-white/20 p-4 backdrop-blur-sm"
          @click="handleTaskClick"
        >
          <view class="flex items-center justify-between">
            <view class="flex items-center">
              <view class="mr-3 h-8 w-8 flex items-center justify-center rounded-full bg-white/30">
                <uni-icons type="gift" size="16" color="#fff" />
              </view>
              <view>
                <text class="mb-1 block font-semibold">{{ taskProgress.taskName }}</text>
                <text class="text-sm opacity-80">{{ taskProgress.taskDesc }}</text>
              </view>
            </view>
            <uni-icons type="right" size="16" color="#fff" />
          </view>
        </view>
      </view>
    </view>

    <!-- 设置菜单 -->
    <view class="mx-6 mb-6">
      <view class="overflow-hidden rounded-xl bg-white/90 backdrop-blur-sm">
        <view
          v-for="(item, index) in settingsList"
          :key="index"
          class="flex items-center justify-between p-4 transition-all"
          :class="index < settingsList.length - 1 ? 'border-b border-gray-100' : ''"
          @click="handleSettingClick(item)"
        >
          <view class="flex items-center">
            <uni-icons :type="item.icon" size="20" color="#6B7280" class="mr-3" />
            <text class="text-gray-800">{{ item.title }}</text>
          </view>
          <uni-icons type="right" size="16" color="#9CA3AF" />
        </view>
      </view>
    </view>

    <!-- 退出登录按钮 -->
    <view class="mx-6 mb-8">
      <wd-button
        type="error"
        size="large"
        block
        custom-style="border-radius: 12px; background: linear-gradient(135deg, #EF4444 0%, #DC2626 100%);"
        @click="handleLogout"
      >
        退出登录
      </wd-button>
    </view>

    <!-- 底部安全距离 -->
    <view class="h-8" />
  </view>
</template>

<style lang="scss" scoped>
// 网格布局
.grid {
  display: grid;
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.gap-4 {
  gap: 1rem;
}

// 渐变背景
.bg-gradient-to-br {
  background: linear-gradient(to bottom right, var(--tw-gradient-stops));
}

.bg-gradient-to-r {
  background: linear-gradient(to right, var(--tw-gradient-stops));
}

.from-purple-400 {
  --tw-gradient-from: #a855f7;
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(168, 85, 247, 0));
}

.via-blue-300 {
  --tw-gradient-stops: var(--tw-gradient-from), #93c5fd, var(--tw-gradient-to, rgba(147, 197, 253, 0));
}

.to-green-200 {
  --tw-gradient-to: #bbf7d0;
}

.from-blue-500 {
  --tw-gradient-from: #3b82f6;
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(59, 130, 246, 0));
}

.to-blue-600 {
  --tw-gradient-to: #2563eb;
}

.from-blue-400 {
  --tw-gradient-from: #60a5fa;
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(96, 165, 250, 0));
}

.to-cyan-400 {
  --tw-gradient-to: #22d3ee;
}

// 毛玻璃效果
.backdrop-blur-sm {
  backdrop-filter: blur(4px);
}

// 过渡动画
.transition-all {
  transition: all 0.3s ease;
}

.duration-300 {
  transition-duration: 300ms;
}
</style>
