<route lang="jsonc" type="home">
{
  "style": {
    "navigationStyle": "custom",
    "navigationBarTitleText": "首页"
  }
}
</route>

<script lang="ts" setup>
import { onMounted, ref } from 'vue'

// 用户信息
const userInfo = ref({
  avatar: '/static/images/default-avatar.png',
  nickname: 'Uninauto',
  idNumber: '微信身份认证',
})

// 活动列表
const activityList = ref([
  {
    id: 1,
    image: '/static/images/activity1.jpg', // 建议使用真实、高质量的图片
    title: '三点班组',
    subtitle: '主题图',
    author: '官方活动',
    time: '刚刚',
  },
  {
    id: 2,
    image: '/static/images/activity2.jpg',
    title: 'Tab Boror',
    subtitle: '活动通',
    author: '力哥',
    time: '12分钟前',
  },
  {
    id: 3,
    image: '/static/images/activity3.jpg',
    title: '远程三不许话',
    subtitle: '活动通',
    author: '远程团队',
    time: '1小时前',
  },
  {
    id: 4,
    image: '/static/images/activity4.jpg',
    title: 'Yabt激效',
    subtitle: '激效通',
    author: '设计部',
    time: '5小时前',
  },
])

// 点击活动
function handleActivityClick(activity: any) {
  uni.showToast({
    title: `查看活动: ${activity.title}`,
    icon: 'none',
  })
}

// 点击身份卡
function handleUserCardClick() {
  uni.navigateTo({
    url: '/pages/me/me',
  })
}

onMounted(() => {
  // TODO: 从接口获取用户数据
})
</script>

<template>
  <view class="home-page-v2">
    <view class="header-section">
      <view class="main-title">
        <view class="title-line1">
          不老联盟!
        </view>
        <view class="title-line2">
          青春不打烊
        </view>
      </view>
    </view>

    <view class="content-section">
      <view class="user-card" @click="handleUserCardClick">
        <view class="user-info">
          <image class="avatar" :src="userInfo.avatar" mode="aspectFill" />
          <view class="user-details">
            <view class="nickname">
              {{ userInfo.nickname }}
            </view>
            <view class="id-number">
              {{ userInfo.idNumber }}
            </view>
          </view>
        </view>
        <view class="join-btn">
          <text>联动者</text>
        </view>
      </view>

      <view class="activity-section">
        <view class="section-header">
          <view class="section-title">
            热门活动
          </view>
          <view class="more-btn">
            <text>刷新</text>
          </view>
        </view>

        <view class="activity-grid">
          <view
            v-for="activity in activityList"
            :key="activity.id"
            class="activity-card"
            :style="{ backgroundImage: `url(${activity.image})` }"
            @click="handleActivityClick(activity)"
          >
            <view class="gradient-overlay" />
            <view class="activity-content">
              <view class="activity-title">
                {{ activity.title }}
              </view>
              <view class="activity-meta">
                <text class="author-name">{{ activity.author }}</text>
                <text class="activity-time">{{ activity.time }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
// 页面背景
.home-page-v2 {
  // 从纯白过渡到浅灰，增加页面深度
  background: linear-gradient(to bottom, #ffffff, #f7f8fa 300rpx);
  min-height: 100vh;
}

// ---------------------------------
// -- 🎨 头部区域 (氛围感升级)
// ---------------------------------
.header-section {
  position: relative;
  // 更具活力的“珊瑚橘”渐变
  background: linear-gradient(135deg, #ffb347 0%, #ff6b6b 100%);
  padding: 100rpx 40rpx 140rpx;
  border-radius: 0 0 80rpx 80rpx;
  overflow: hidden; // 隐藏装饰性伪元素溢出的部分

  // 添加抽象背景圆圈作为装饰，增加设计感
  &::before,
  &::after {
    content: '';
    position: absolute;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.1);
    z-index: 1;
  }
  &::before {
    width: 300rpx;
    height: 300rpx;
    top: -100rpx;
    right: -100rpx;
  }
  &::after {
    width: 150rpx;
    height: 150rpx;
    bottom: 20rpx;
    left: -50rpx;
  }

  .main-title {
    position: relative;
    z-index: 2;
    .title-line1,
    .title-line2 {
      font-size: 56rpx;
      font-weight: bold;
      color: #fff;
      line-height: 1.3;
      text-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
    }
  }
}

.content-section {
  padding: 0 30rpx;
}

// ---------------------------------
// -- ✨ 身份卡 (玻璃拟态效果)
// ---------------------------------
.user-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: -100rpx; // 向上移动更多，重叠效果更强
  position: relative;
  z-index: 10;
  padding: 40rpx;
  border-radius: 32rpx;

  // 核心：玻璃拟态效果
  background-color: rgba(255, 255, 255, 0.7); // 半透明背景
  backdrop-filter: blur(20rpx); // 毛玻璃模糊效果
  border: 1rpx solid rgba(255, 255, 255, 0.5); // 模拟玻璃边缘高光
  box-shadow: 0 16rpx 40rpx rgba(100, 100, 100, 0.12); // 更柔和、更弥散的阴影

  .user-info {
    display: flex;
    align-items: center;
    gap: 24rpx;

    .avatar {
      width: 110rpx;
      height: 110rpx;
      border-radius: 50%;
      border: 4rpx solid #fff;
    }
    .user-details {
      .nickname {
        font-size: 36rpx;
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 8rpx;
      }
      .id-number {
        font-size: 24rpx;
        color: #4a5568; // 稍深一点的灰色，在半透明背景上更清晰
      }
    }
  }

  .join-btn {
    padding: 18rpx 36rpx;
    background: linear-gradient(135deg, #ffb347 0%, #ff6b6b 100%);
    color: #fff;
    border-radius: 30rpx;
    font-size: 26rpx;
    font-weight: 500;
    box-shadow: 0 8rpx 16rpx rgba(255, 125, 106, 0.35);
  }
}

// ---------------------------------
// -- 🚀 活动列表 (图文融合设计)
// ---------------------------------
.activity-section {
  margin-top: 50rpx;
  padding-bottom: 120rpx;

  .section-header {
    margin-bottom: 30rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .section-title {
      font-size: 38rpx;
      font-weight: 600;
      color: #1f2937;
    }
    .more-btn {
      font-size: 26rpx;
      color: #9ca3af;
    }
  }

  .activity-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24rpx;

    .activity-card {
      position: relative;
      height: 320rpx; // 增加卡片高度，让图片展示更充分
      border-radius: 28rpx; // 更大的圆角
      overflow: hidden;
      background-size: cover; // 背景图覆盖整个区域
      background-position: center; // 背景图居中显示
      background-color: #e5e7eb; // 图片加载失败时的占位色
      color: #ffffff;
      display: flex;
      flex-direction: column;
      justify-content: flex-end; // 内容垂直底部对齐
      transition: transform 0.25s ease;

      &:active {
        transform: scale(0.96);
      }

      // 核心：渐变蒙版，用于压暗图片底部，凸显文字
      .gradient-overlay {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 60%;
        background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
        z-index: 1;
      }

      // 活动文字内容
      .activity-content {
        position: relative;
        z-index: 2;
        padding: 24rpx;

        .activity-title {
          font-size: 30rpx;
          font-weight: bold;
          line-height: 1.3;
          // 限制最多显示两行
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .activity-meta {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-top: 16rpx;
          font-size: 22rpx;
          color: rgba(255, 255, 255, 0.85); // 使用半透明白色
        }
      }
    }
  }
}
</style>
